// ignore_for_file: avoid_print

import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CompassWidget extends StatefulWidget {
  final String compassImagePath;

  const CompassWidget({
    super.key,
    required this.compassImagePath,
  });

  @override
  State<CompassWidget> createState() => _CompassWidgetState();
}

class _CompassWidgetState extends State<CompassWidget> {
  static const EventChannel _compassChannel = EventChannel('compass_stream');
  late final Stream<double> _compassStream;

  @override
  void initState() {
    super.initState();
    _compassStream = _compassChannel.receiveBroadcastStream().map((event) {
      if (event is double) return event;
      if (event is int) return event.toDouble();
      debugPrint('Dữ liệu không phải kiểu số: $event (${event.runtimeType})');
      return 0.0;
    }).timeout(
      const Duration(seconds: 5),
      onTimeout: (sink) {
        debugPrint('Compass stream timeout after 5 seconds');
        sink.close();
      },
    ).handleError((error) {
      debugPrint('Lỗi compass stream: $error');
      return 0.0; // Giá trị mặc định khi lỗi
    });
  }

  @override
  void dispose() {
    // EventChannel không cần dispose thủ công, nhưng giữ để mở rộng nếu cần
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<double>(
      stream: _compassStream,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          debugPrint('Lỗi stream trong build: ${snapshot.error}');
          // Thay vì hiển thị lỗi, hiển thị ảnh la bàn tĩnh
          double size = MediaQuery.of(context).size.width * 0.8;
          debugPrint('Stream error, falling back to static compass image');
          return _buildCompassImage(size);
        }

        final double? filteredAngle = snapshot.data;

        if (filteredAngle == null) {
          // Thay vì hiển thị lỗi, hiển thị ảnh la bàn tĩnh
          double size = MediaQuery.of(context).size.width * 0.8;
          debugPrint('No sensor data, falling back to static compass image');
          return _buildCompassImage(size);
        }

        // Sử dụng kích thước linh hoạt dựa trên chiều rộng màn hình
        double size = MediaQuery.of(context).size.width * 0.8; // 80% chiều rộng

        // Debug: In ra đường dẫn ảnh
        debugPrint('Loading compass image: ${widget.compassImagePath}');

        return Transform.rotate(
          angle: (filteredAngle * math.pi / 180) *
              -1, // Đổi sang radian và xoay ngược
          child: _buildCompassImage(size),
        );
      },
    );
  }

  Widget _buildCompassImage(double size) {
    // Kiểm tra nếu đường dẫn ảnh trống hoặc null
    if (widget.compassImagePath.isEmpty) {
      debugPrint('Compass image path is empty, using fallback');
      return _buildFallbackCompass(size);
    }

    return Image.asset(
      widget.compassImagePath,
      height: size,
      width: size,
      fit: BoxFit.contain,
      errorBuilder: (context, error, stackTrace) {
        debugPrint('Lỗi tải hình ảnh la bàn: $error');
        debugPrint('Đường dẫn ảnh: ${widget.compassImagePath}');
        debugPrint('Stack trace: $stackTrace');

        // Thử load ảnh fallback
        return _buildFallbackCompass(size);
      },
    );
  }

  Widget _buildFallbackCompass(double size) {
    // Thử load ảnh compass mặc định
    return Image.asset(
      'assets/images/compass.png',
      height: size,
      width: size,
      fit: BoxFit.contain,
      errorBuilder: (context, error, stackTrace) {
        debugPrint('Fallback compass image also failed: $error');
        // Nếu ảnh fallback cũng lỗi, hiển thị icon
        return _buildIconCompass(size);
      },
    );
  }

  Widget _buildIconCompass(double size) {
    return Container(
      height: size,
      width: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 2),
        color: Colors.black.withValues(alpha: 0.3),
      ),
      child: Stack(
        children: [
          // Vòng tròn ngoài
          Center(
            child: Container(
              height: size * 0.9,
              width: size * 0.9,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white70, width: 1),
              ),
            ),
          ),
          // Kim la bàn
          Center(
            child: Container(
              height: size * 0.6,
              width: 4,
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          // Điểm trung tâm
          Center(
            child: Container(
              height: 12,
              width: 12,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
              ),
            ),
          ),
          // Chữ N
          Positioned(
            top: size * 0.05,
            left: size * 0.5 - 8,
            child: const Text(
              'N',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

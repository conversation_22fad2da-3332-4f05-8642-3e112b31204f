import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class SensorTestScreen extends StatefulWidget {
  const SensorTestScreen({super.key});

  @override
  State<SensorTestScreen> createState() => _SensorTestScreenState();
}

class _SensorTestScreenState extends State<SensorTestScreen> {
  static const EventChannel _compassChannel = EventChannel('compass_stream');
  double? _lastAngle;
  String _status = 'Chưa kết nối';
  String _error = '';

  @override
  void initState() {
    super.initState();
    _startListening();
  }

  void _startListening() {
    setState(() {
      _status = 'Đang kết nối...';
      _error = '';
    });

    _compassChannel.receiveBroadcastStream().listen(
      (dynamic event) {
        debugPrint('Sensor data received: $event');
        setState(() {
          _lastAngle = event as double?;
          _status = 'Đã kết nối';
          _error = '';
        });
      },
      onError: (dynamic error) {
        debugPrint('Sensor error: $error');
        setState(() {
          _status = 'Lỗi';
          _error = error.toString();
        });
      },
      onDone: () {
        debugPrint('Sensor stream done');
        setState(() {
          _status = 'Đã ngắt kết nối';
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Cảm Biến'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Trạng thái cảm biến:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Status: $_status',
                      style: TextStyle(
                        fontSize: 16,
                        color: _status == 'Đã kết nối' ? Colors.green : Colors.red,
                      ),
                    ),
                    if (_error.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text(
                        'Error: $_error',
                        style: const TextStyle(fontSize: 14, color: Colors.red),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Dữ liệu cảm biến:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Góc hiện tại: ${_lastAngle?.toStringAsFixed(2) ?? 'Không có dữ liệu'}°',
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Hướng: ${_getDirection(_lastAngle)}',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Hướng dẫn:',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '1. Nếu status = "Đã kết nối" và có dữ liệu góc → Cảm biến hoạt động OK',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      '2. Nếu status = "Lỗi" → Kiểm tra quyền truy cập cảm biến',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      '3. Nếu không có dữ liệu → CompassWidget sẽ hiển thị thông báo lỗi',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Center(
              child: ElevatedButton(
                onPressed: _startListening,
                child: const Text('Thử lại kết nối'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getDirection(double? angle) {
    if (angle == null) return 'Không xác định';
    
    if (angle >= 337.5 || angle < 22.5) return 'Bắc (N)';
    if (angle >= 22.5 && angle < 67.5) return 'Đông Bắc (NE)';
    if (angle >= 67.5 && angle < 112.5) return 'Đông (E)';
    if (angle >= 112.5 && angle < 157.5) return 'Đông Nam (SE)';
    if (angle >= 157.5 && angle < 202.5) return 'Nam (S)';
    if (angle >= 202.5 && angle < 247.5) return 'Tây Nam (SW)';
    if (angle >= 247.5 && angle < 292.5) return 'Tây (W)';
    if (angle >= 292.5 && angle < 337.5) return 'Tây Bắc (NW)';
    
    return 'Không xác định';
  }
}

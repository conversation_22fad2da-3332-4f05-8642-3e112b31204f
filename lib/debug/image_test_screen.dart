import 'package:flutter/material.dart';

class ImageTestScreen extends StatelessWidget {
  const ImageTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Load Ảnh La Bàn'),
        backgroundColor: Colors.blue,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Colum<PERSON>(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Test La Bàn Cơ Bản:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            _buildImageTest(
              'assets/images/compass.png',
              'La bàn cơ bản',
            ),

            const SizedBox(height: 30),
            const Text(
              'Test La Bàn Theo Tuổi:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),

            // Test tất cả ảnh la bàn theo tuổi
            _buildImageTest(
              'assets/images_compass/lb_e1.png',
              'La bàn E1 (<PERSON><PERSON><PERSON>)',
            ),
            _buildImageTest(
              'assets/images_compass/lb_w2.png',
              'La bàn W2 (Tây Tứ Mệnh)',
            ),
            _buildImageTest(
              'assets/images_compass/lb_e3.png',
              'La bàn E3 (Đông Tứ Mệnh)',
            ),
            _buildImageTest(
              'assets/images_compass/lb_e4.png',
              'La bàn E4 (Đông Tứ Mệnh)',
            ),
            _buildImageTest(
              'assets/images_compass/lb_w6.png',
              'La bàn W6 (Tây Tứ Mệnh)',
            ),
            _buildImageTest(
              'assets/images_compass/lb_w7.png',
              'La bàn W7 (Tây Tứ Mệnh)',
            ),
            _buildImageTest(
              'assets/images_compass/lb_w8.png',
              'La bàn W8 (Tây Tứ Mệnh)',
            ),
            _buildImageTest(
              'assets/images_compass/lb_e9.png',
              'La bàn E9 (Đông Tứ Mệnh)',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageTest(String imagePath, String description) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            description,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Path: $imagePath',
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          ),
          const SizedBox(height: 10),
          Center(
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.black),
                borderRadius: BorderRadius.circular(100),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(100),
                child: Image.asset(
                  imagePath,
                  width: 200,
                  height: 200,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    debugPrint('❌ Lỗi load ảnh: $imagePath');
                    debugPrint('Error: $error');
                    debugPrint('StackTrace: $stackTrace');

                    return Container(
                      width: 200,
                      height: 200,
                      color: Colors.red.withValues(alpha: 0.1),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error,
                            color: Colors.red,
                            size: 48,
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'KHÔNG LOAD ĐƯỢC',
                            style: TextStyle(
                              color: Colors.red,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            error.toString(),
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.red,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  },
                  frameBuilder:
                      (context, child, frame, wasSynchronouslyLoaded) {
                    if (wasSynchronouslyLoaded || frame != null) {
                      debugPrint('✅ Ảnh load thành công: $imagePath');
                      return child;
                    }
                    return SizedBox(
                      width: 200,
                      height: 200,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

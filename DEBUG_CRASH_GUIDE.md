# Debug Guide - App Crash Fix

## Các lỗi đã được sửa:

### 1. **Facebook SDK Initialization trong main()**
- **Vấn đề**: Facebook Analytics được gọi trong main() function trước khi Flutter engine khởi tạo hoàn toàn
- **Giải pháp**: Di chuyển logic vào HomeScreen.initState()

### 2. **Thiếu ProGuard Rules**
- **Vấn đề**: Facebook SDK bị obfuscate trong release build
- **Giải pháp**: Tạo `android/app/proguard-rules.pro` với rules cho Facebook SDK

### 3. **Thiếu Error Handling**
- **Vấn đề**: Crash khi Facebook SDK gặp lỗi
- **Giải pháp**: Thêm try-catch blocks cho tất cả Facebook Analytics calls

### 4. **lateinit var crash**
- **Vấn đề**: `appEventsLogger` c<PERSON> thể chưa được khởi tạo
- **Gi<PERSON>i pháp**: <PERSON><PERSON><PERSON><PERSON> thành nullable và sử dụng safe calls

## Cách kiểm tra lỗi nếu app vẫn crash:

### 1. **Kiểm tra logs qua ADB:**
```bash
# Kết nối điện thoại và enable USB debugging
adb devices

# Xem logs realtime
adb logcat | grep -E "(AndroidRuntime|MainActivity|Facebook|Flutter)"

# Hoặc lọc chỉ errors
adb logcat *:E
```

### 2. **Kiểm tra crash logs cụ thể:**
```bash
# Clear logs trước
adb logcat -c

# Mở app và để crash
# Sau đó xem logs
adb logcat -d > crash_log.txt
```

### 3. **Test từng bước:**

#### Bước 1: Test app không có Facebook SDK
- Comment out tất cả Facebook related code trong MainActivity
- Build và test

#### Bước 2: Test với Facebook SDK nhưng không có analytics
- Chỉ giữ lại Facebook SDK initialization
- Comment out tất cả analytics calls

#### Bước 3: Test với analytics nhưng không có UI calls
- Comment out Facebook calls trong HomeScreen
- Chỉ giữ lại trong MainActivity

### 4. **Kiểm tra dependencies:**
```bash
# Kiểm tra Facebook SDK version
cd android
./gradlew app:dependencies | grep facebook
```

### 5. **Build debug để test:**
```bash
# Build debug version để dễ debug hơn
flutter build apk --debug

# Hoặc run trực tiếp
flutter run --release
```

## Các file đã được sửa:

1. **lib/main.dart**: Loại bỏ async/await trong main()
2. **lib/screen/home_screen.dart**: Thêm first launch check
3. **android/app/src/main/kotlin/.../MainActivity.kt**: Thêm error handling
4. **android/app/proguard-rules.pro**: Thêm ProGuard rules
5. **lib/services/facebook_analytics_service.dart**: Thêm error handling

## Nếu vẫn crash:

### Option 1: Disable Facebook Analytics tạm thời
```kotlin
// Trong MainActivity.kt, comment out:
// FacebookSdk.sdkInitialize(applicationContext)
// AppEventsLogger.activateApp(application)
```

### Option 2: Sử dụng Firebase Analytics thay thế
```yaml
# Trong pubspec.yaml
dependencies:
  firebase_analytics: ^10.7.4
```

### Option 3: Kiểm tra permissions
```xml
<!-- Trong AndroidManifest.xml, đảm bảo có: -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

## Logs quan trọng cần tìm:

1. **Facebook SDK errors**: `grep -i facebook`
2. **Method channel errors**: `grep -i "method.*channel"`
3. **Native crashes**: `grep -i "androidruntime"`
4. **Flutter engine errors**: `grep -i flutter`

## Contact Debug:
Nếu vẫn gặp vấn đề, hãy gửi:
1. File `crash_log.txt` từ adb logcat
2. Thông tin thiết bị (Android version, RAM, etc.)
3. Các bước tái tạo lỗi

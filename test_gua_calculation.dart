import 'dart:convert';
import 'dart:io';

// Copy GuaCalculator class để test
class GuaCalculator {
  static Map<String, dynamic> determineMansion(int yearOfBirth, String gender) {
    int lastTwoDigitsSum = (yearOfBirth % 10) + ((yearOfBirth ~/ 10) % 10);

    if (lastTwoDigitsSum >= 10) {
      lastTwoDigitsSum = (lastTwoDigitsSum % 10) + (lastTwoDigitsSum ~/ 10);
    }

    int guaNumber;
    if (gender.toLowerCase() == 'male') {
      guaNumber =
          yearOfBirth < 2000 ? (10 - lastTwoDigitsSum) : (9 - lastTwoDigitsSum);
    } else if (gender.toLowerCase() == 'female') {
      guaNumber =
          yearOfBirth < 2000 ? (5 + lastTwoDigitsSum) : (6 + lastTwoDigitsSum);
    } else {
      return {
        'error': 'Giớ<PERSON> tính không hợp lệ. <PERSON><PERSON><PERSON> nhập "male" hoặc "female".'
      };
    }

    if (guaNumber >= 10) {
      guaNumber = (guaNumber % 10) + (guaNumber ~/ 10);
    }

    if (guaNumber == 5) {
      if (gender.toLowerCase() == 'male') {
        guaNumber = 2;
      } else if (gender.toLowerCase() == 'female') {
        guaNumber = 8;
      }
    }

    String mansion =
        ([1, 3, 4, 9].contains(guaNumber)) ? 'Đông Tứ Mệnh' : 'Tây Tứ Mệnh';

    return {'guaNumber': guaNumber, 'mansion': mansion};
  }
}

String getJsonFileNameFromGua(int guaNumber) {
  Map<int, String> fileMapping = {
    1: 'e1_data.json',
    2: 'w2_data.json',
    3: 'e3_data.json',
    4: 'e4_data.json',
    6: 'w6_data.json',
    7: 'w7_data.json',
    8: 'w8_data.json',
    9: 'e9_data.json',
  };

  return fileMapping[guaNumber] ?? 'e1_data.json';
}

void main() async {
  print('=== TEST TÍNH TOÁN GUA NUMBER ===\n');

  // Test cases
  final testCases = [
    {'year': 1990, 'gender': 'male', 'genderVN': 'Nam'},
    {'year': 1990, 'gender': 'female', 'genderVN': 'Nữ'},
    {'year': 1985, 'gender': 'male', 'genderVN': 'Nam'},
    {'year': 1985, 'gender': 'female', 'genderVN': 'Nữ'},
    {'year': 2000, 'gender': 'male', 'genderVN': 'Nam'},
    {'year': 2000, 'gender': 'female', 'genderVN': 'Nữ'},
    {'year': 1995, 'gender': 'male', 'genderVN': 'Nam'},
    {'year': 1995, 'gender': 'female', 'genderVN': 'Nữ'},
  ];

  for (var testCase in testCases) {
    final year = testCase['year'] as int;
    final gender = testCase['gender'] as String;
    final genderVN = testCase['genderVN'] as String;

    print('Test: $genderVN sinh năm $year');
    
    final result = GuaCalculator.determineMansion(year, gender);
    
    if (result.containsKey('error')) {
      print('  ❌ Lỗi: ${result['error']}');
    } else {
      final guaNumber = result['guaNumber'];
      final mansion = result['mansion'];
      final jsonFile = getJsonFileNameFromGua(guaNumber);
      
      print('  ✅ Quái số: $guaNumber');
      print('  ✅ Mệnh: $mansion');
      print('  ✅ File JSON: $jsonFile');
      
      // Kiểm tra file JSON có tồn tại không
      final jsonPath = 'lib/data/$jsonFile';
      final jsonFileObj = File(jsonPath);
      if (await jsonFileObj.exists()) {
        print('  ✅ File JSON tồn tại');
        
        // Đọc và kiểm tra đường dẫn ảnh
        try {
          final content = await jsonFileObj.readAsString();
          final data = json.decode(content);
          final compassPath = data['compass']?[0]?['co'];
          
          if (compassPath != null) {
            print('  ✅ Đường dẫn ảnh: $compassPath');
            
            // Kiểm tra file ảnh
            final imageFile = File(compassPath);
            if (await imageFile.exists()) {
              print('  ✅ File ảnh tồn tại');
            } else {
              print('  ❌ File ảnh KHÔNG tồn tại');
            }
          } else {
            print('  ❌ Không tìm thấy đường dẫn ảnh trong JSON');
          }
        } catch (e) {
          print('  ❌ Lỗi đọc JSON: $e');
        }
      } else {
        print('  ❌ File JSON KHÔNG tồn tại');
      }
    }
    print('');
  }

  // Test edge cases
  print('=== TEST EDGE CASES ===\n');
  
  // Test guaNumber = 0 (invalid user info)
  print('Test: guaNumber = 0 (thông tin không hợp lệ)');
  final fallbackFile = getJsonFileNameFromGua(0);
  print('  Fallback file: $fallbackFile');
  
  final fallbackPath = 'lib/data/$fallbackFile';
  final fallbackFileObj = File(fallbackPath);
  if (await fallbackFileObj.exists()) {
    print('  ✅ Fallback file tồn tại');
    
    try {
      final content = await fallbackFileObj.readAsString();
      final data = json.decode(content);
      final compassPath = data['compass']?[0]?['co'];
      
      if (compassPath != null) {
        print('  ✅ Fallback compass path: $compassPath');
        
        final imageFile = File(compassPath);
        if (await imageFile.exists()) {
          print('  ✅ Fallback image tồn tại');
        } else {
          print('  ❌ Fallback image KHÔNG tồn tại');
        }
      }
    } catch (e) {
      print('  ❌ Lỗi đọc fallback JSON: $e');
    }
  } else {
    print('  ❌ Fallback file KHÔNG tồn tại');
  }

  print('\n=== KẾT THÚC TEST ===');
}

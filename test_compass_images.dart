import 'dart:io';
import 'dart:convert';

void main() async {
  print('=== KIỂM TRA ẢNH LA BÀN ===\n');
  
  // 1. <PERSON><PERSON>m tra ảnh la bàn cơ bản
  print('1. <PERSON>ểm tra ảnh la bàn cơ bản:');
  final basicCompassPath = 'assets/images/compass.png';
  final basicCompassFile = File(basicCompassPath);
  if (await basicCompassFile.exists()) {
    print('✅ $basicCompassPath - TỒN TẠI');
  } else {
    print('❌ $basicCompassPath - KHÔNG TỒN TẠI');
  }
  
  // 2. Kiểm tra thư mục ảnh la bàn theo tuổi
  print('\n2. Kiểm tra ảnh la bàn theo tuổi:');
  final compassDir = Directory('assets/images_compass');
  if (await compassDir.exists()) {
    print('✅ Thư mục assets/images_compass tồn tại');
    
    final files = await compassDir.list().toList();
    final pngFiles = files.where((f) => f.path.endsWith('.png')).toList();
    
    print('Tìm thấy ${pngFiles.length} file PNG:');
    for (var file in pngFiles) {
      final fileName = file.path.split('/').last;
      print('  - $fileName');
    }
    
    // Kiểm tra các file cụ thể từ JSON
    final expectedFiles = [
      'lb_e1.png', 'lb_w2.png', 'lb_e3.png', 'lb_e4.png',
      'lb_w6.png', 'lb_w7.png', 'lb_w8.png', 'lb_e9.png'
    ];
    
    print('\nKiểm tra file cụ thể từ JSON:');
    for (var fileName in expectedFiles) {
      final filePath = 'assets/images_compass/$fileName';
      final file = File(filePath);
      if (await file.exists()) {
        print('✅ $fileName');
      } else {
        print('❌ $fileName - THIẾU');
      }
    }
  } else {
    print('❌ Thư mục assets/images_compass không tồn tại');
  }
  
  // 3. Kiểm tra file JSON
  print('\n3. Kiểm tra file JSON:');
  final jsonFiles = [
    'e1_data.json', 'w2_data.json', 'e3_data.json', 'e4_data.json',
    'w6_data.json', 'w7_data.json', 'w8_data.json', 'e9_data.json'
  ];
  
  for (var jsonFile in jsonFiles) {
    final filePath = 'lib/data/$jsonFile';
    final file = File(filePath);
    if (await file.exists()) {
      print('✅ $jsonFile');
      
      // Đọc và kiểm tra đường dẫn ảnh trong JSON
      try {
        final content = await file.readAsString();
        final data = json.decode(content);
        final compassPath = data['compass']?[0]?['co'];
        if (compassPath != null) {
          print('   Đường dẫn ảnh: $compassPath');
          
          // Kiểm tra file ảnh có tồn tại không
          final imageFile = File(compassPath);
          if (await imageFile.exists()) {
            print('   ✅ Ảnh tồn tại');
          } else {
            print('   ❌ Ảnh không tồn tại');
          }
        } else {
          print('   ❌ Không tìm thấy đường dẫn ảnh trong JSON');
        }
      } catch (e) {
        print('   ❌ Lỗi đọc JSON: $e');
      }
    } else {
      print('❌ $jsonFile - KHÔNG TỒN TẠI');
    }
  }
  
  // 4. Kiểm tra pubspec.yaml
  print('\n4. Kiểm tra pubspec.yaml:');
  final pubspecFile = File('pubspec.yaml');
  if (await pubspecFile.exists()) {
    final content = await pubspecFile.readAsString();
    if (content.contains('assets/images/')) {
      print('✅ assets/images/ được khai báo');
    } else {
      print('❌ assets/images/ chưa được khai báo');
    }
    
    if (content.contains('assets/images_compass/')) {
      print('✅ assets/images_compass/ được khai báo');
    } else {
      print('❌ assets/images_compass/ chưa được khai báo');
    }
    
    if (content.contains('lib/data/')) {
      print('✅ lib/data/ được khai báo');
    } else {
      print('❌ lib/data/ chưa được khai báo');
    }
  }
  
  print('\n=== KẾT THÚC KIỂM TRA ===');
}

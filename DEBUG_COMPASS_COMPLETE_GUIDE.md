# Complete Debug Guide - Compass Image Display Issue

## 🎯 **Mụ<PERSON> tiêu:**
Tìm và sửa lỗi không hiển thị ảnh la bàn trong app

## 🔍 **Phân tích vấn đề có thể:**

### 1. **Vấn đề cảm biến (NGHI NGỜ CHÍNH)**
- CompassWidget phụ thuộc vào stream data từ cảm biến
- Nếu không có dữ liệu cảm biến → widget hiển thị thông báo lỗi thay vì ảnh
- **Kiểm tra**: Sử dụng SensorTestScreen

### 2. **Vấn đề load ảnh**
- File ảnh không tồn tại hoặc đường dẫn sai
- Assets không được bundle vào APK
- **Kiểm tra**: Sử dụng ImageTestScreen

### 3. **Vấn đề thứ tự Stack**
- Ảnh overlay che khuất ảnh la bàn
- **<PERSON><PERSON> sửa**: Đổi thứ tự trong screen_compass_8.dart

## 🛠️ **Debug Tools đã tạo:**

### 1. **SensorTestScreen** 🔧
**Mục đích**: Kiểm tra cảm biến hoạt động
**Truy cập**: FloatingActionButton → "Test Cảm Biến"

**Thông tin hiển thị**:
- ✅ Trạng thái kết nối cảm biến
- ✅ Dữ liệu góc hiện tại
- ✅ Hướng la bàn (N, NE, E, etc.)
- ✅ Thông báo lỗi nếu có

**Kết quả mong đợi**:
- Status: "Đã kết nối"
- Góc: Thay đổi khi xoay điện thoại
- Hướng: Hiển thị đúng

### 2. **ImageTestScreen** 🖼️
**Mục đích**: Kiểm tra việc load ảnh
**Truy cập**: FloatingActionButton → "Test Ảnh La Bàn"

**Thông tin hiển thị**:
- ✅ Test load ảnh la bàn cơ bản
- ✅ Test load tất cả ảnh la bàn theo tuổi
- ✅ Test StaticCompassWidget
- ✅ Hiển thị lỗi chi tiết nếu có

### 3. **StaticCompassWidget** 🎯
**Mục đích**: Test ảnh la bàn không phụ thuộc cảm biến
**Vị trí**: Trong CompassDetailScreen (la bàn cơ bản)

**Tính năng**:
- ✅ Không cần dữ liệu cảm biến
- ✅ 3-level fallback system
- ✅ Debug logging chi tiết

### 4. **Enhanced CompassDetailScreen** 📱
**Cải tiến**: Hiển thị cả 2 loại la bàn để so sánh
- **La bàn với stream**: Phụ thuộc cảm biến (có thể lỗi)
- **La bàn tĩnh**: Không phụ thuộc cảm biến (để test ảnh)

## 📋 **Hướng dẫn debug từng bước:**

### **Bước 1: Cài đặt APK debug**
```bash
adb install build/app/outputs/flutter-apk/app-debug.apk
```

### **Bước 2: Test cảm biến**
1. Mở app
2. Nhấn FloatingActionButton (icon bug màu xanh)
3. Chọn "Test Cảm Biến"
4. **Kiểm tra**:
   - Status có phải "Đã kết nối"?
   - Có dữ liệu góc không?
   - Góc có thay đổi khi xoay điện thoại?

### **Bước 3: Test ảnh la bàn**
1. Từ debug menu, chọn "Test Ảnh La Bàn"
2. **Kiểm tra**:
   - Ảnh nào hiển thị được?
   - Ảnh nào bị lỗi?
   - StaticCompassWidget có hoạt động?

### **Bước 4: Test la bàn cơ bản**
1. Quay về HomeScreen
2. Nhấn "La bàn cơ bản"
3. **Kiểm tra**:
   - La bàn với stream có hiển thị ảnh?
   - La bàn tĩnh có hiển thị ảnh?
   - Cái nào hoạt động?

### **Bước 5: Test la bàn theo tuổi**
1. Nhập thông tin (Nam/Nữ + năm sinh)
2. Nhấn "La bàn theo tuổi"
3. **Kiểm tra**:
   - Có loading indicator không?
   - Ảnh la bàn có hiển thị?
   - Ảnh có đúng với guaNumber?

## 🔍 **Phân tích kết quả:**

### **Nếu SensorTestScreen báo lỗi:**
- ❌ **Nguyên nhân**: Cảm biến không hoạt động
- ✅ **Giải pháp**: Sử dụng StaticCompassWidget thay thế

### **Nếu ImageTestScreen báo lỗi:**
- ❌ **Nguyên nhân**: File ảnh không tồn tại hoặc không được bundle
- ✅ **Giải pháp**: Kiểm tra pubspec.yaml và rebuild

### **Nếu la bàn tĩnh hoạt động nhưng la bàn stream không:**
- ❌ **Nguyên nhân**: Vấn đề cảm biến
- ✅ **Giải pháp**: Thay thế CompassWidget bằng StaticCompassWidget

### **Nếu cả hai đều không hoạt động:**
- ❌ **Nguyên nhân**: Vấn đề load ảnh
- ✅ **Giải pháp**: Kiểm tra đường dẫn ảnh và assets

## 🚀 **Giải pháp dự kiến:**

### **Giải pháp 1: Thay thế CompassWidget**
Nếu vấn đề là cảm biến, thay thế trong:
- `screen_compass.dart`
- `screen_compass_8.dart`

### **Giải pháp 2: Fix assets**
Nếu vấn đề là ảnh, kiểm tra:
- `pubspec.yaml`
- File ảnh tồn tại
- Rebuild app

### **Giải pháp 3: Hybrid approach**
Sử dụng cả hai:
- StaticCompassWidget làm fallback
- CompassWidget nếu cảm biến hoạt động

## 📱 **Debug logs quan trọng:**

Khi chạy app, tìm các logs:
```
StaticCompassWidget - Loading image: [path]
Sensor data received: [angle]
Compass path from JSON: [path]
Loading compass image: [path]
```

## 🎉 **Kết luận:**

**App bây giờ có đầy đủ debug tools để xác định chính xác nguyên nhân vấn đề!**

**Các tools sẽ giúp xác định:**
1. Cảm biến có hoạt động không?
2. Ảnh có load được không?
3. Widget nào hoạt động?
4. Cần sửa gì?

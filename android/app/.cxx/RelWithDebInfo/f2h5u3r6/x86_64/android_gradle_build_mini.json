{"buildFiles": ["/opt/homebrew/Caskroom/flutter/3.27.4/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/app_dev/compass_vn/android/app/.cxx/RelWithDebInfo/f2h5u3r6/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/app_dev/compass_vn/android/app/.cxx/RelWithDebInfo/f2h5u3r6/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
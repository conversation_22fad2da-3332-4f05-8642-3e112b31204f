package com.hellovietnam.compassapp_vn

import android.Manifest
import android.content.pm.PackageManager
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.os.Bundle
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.facebook.FacebookSdk
import com.facebook.appevents.AppEventsLogger
import com.facebook.appevents.AppEventsConstants
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodChannel
import kotlin.math.atan2

class MainActivity : FlutterActivity() {
    private val CHANNEL = "compass_stream"
    private val ANALYTICS_CHANNEL = "facebook_analytics"
    private lateinit var sensorManager: SensorManager
    private var accelerometer: Sensor? = null
    private var magnetometer: Sensor? = null
    private var accelerometerReading = FloatArray(3)
    private var magnetometerReading = FloatArray(3)
    private var filteredAngle = 0.0
    private val alpha = 0.33
    private var appEventsLogger: AppEventsLogger? = null

    // Permission request code
    private val LOCATION_PERMISSION_REQUEST_CODE = 1001

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            // Initialize Facebook SDK
            FacebookSdk.sdkInitialize(applicationContext)
            AppEventsLogger.activateApp(application)

            // Initialize Facebook Analytics Logger
            appEventsLogger = AppEventsLogger.newLogger(this)

            // Track App Launch Event
            trackAppLaunch()
        } catch (e: Exception) {
            // Log error but don't crash the app
            android.util.Log.e("MainActivity", "Facebook SDK initialization failed", e)
        }

        // Request location permissions for compass
        requestLocationPermissions()
    }

    private fun requestLocationPermissions() {
        val permissions = arrayOf(
            Manifest.permission.ACCESS_COARSE_LOCATION,
            Manifest.permission.ACCESS_FINE_LOCATION
        )

        val permissionsToRequest = permissions.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }

        if (permissionsToRequest.isNotEmpty()) {
            android.util.Log.d("MainActivity", "Requesting location permissions for compass")
            ActivityCompat.requestPermissions(
                this,
                permissionsToRequest.toTypedArray(),
                LOCATION_PERMISSION_REQUEST_CODE
            )
        } else {
            android.util.Log.d("MainActivity", "Location permissions already granted")
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            LOCATION_PERMISSION_REQUEST_CODE -> {
                val allGranted = grantResults.all { it == PackageManager.PERMISSION_GRANTED }
                if (allGranted) {
                    android.util.Log.d("MainActivity", "Location permissions granted - compass should work")
                } else {
                    android.util.Log.w("MainActivity", "Location permissions denied - compass may not work properly")
                }
            }
        }
    }

    private fun trackAppLaunch() {
        try {
            val parameters = Bundle()
            parameters.putString("app_version", "2.2.2")
            parameters.putString("platform", "android")
            appEventsLogger?.logEvent("app_launch", parameters)
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Failed to track app launch", e)
        }
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Setup Facebook Analytics Method Channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, ANALYTICS_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "logEvent" -> {
                    val eventName = call.argument<String>("eventName") ?: ""
                    val parameters = call.argument<Map<String, Any>>("parameters") ?: emptyMap()
                    logFacebookEvent(eventName, parameters)
                    result.success(true)
                }
                "logCompassUsage" -> {
                    logCompassUsage()
                    result.success(true)
                }
                "logScreenView" -> {
                    val screenName = call.argument<String>("screenName") ?: ""
                    logScreenView(screenName)
                    result.success(true)
                }
                else -> result.notImplemented()
            }
        }

        EventChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setStreamHandler(
            object : EventChannel.StreamHandler {
                private var eventSink: EventChannel.EventSink? = null
                private var sensorListener: SensorEventListener? = null

                override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
                    eventSink = events
                    sensorManager = getSystemService(SENSOR_SERVICE) as SensorManager
                    accelerometer = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
                    magnetometer = sensorManager.getDefaultSensor(Sensor.TYPE_MAGNETIC_FIELD)

                    // Check if running on emulator
                    val isEmulator = isRunningOnEmulator()
                    android.util.Log.d("MainActivity", "Is emulator: $isEmulator")

                    if (accelerometer == null || magnetometer == null) {
                        if (isEmulator) {
                            android.util.Log.w("MainActivity", "Sensors not available on emulator, providing fake data")
                            // Provide fake compass data for emulator
                            provideFakeCompassData(events)
                            return
                        } else {
                            eventSink?.error("UNAVAILABLE", "Cảm biến không khả dụng", null)
                            return
                        }
                    }

                    filteredAngle = 0.0 // Reset góc

                    sensorListener = object : SensorEventListener {
                        override fun onSensorChanged(event: SensorEvent?) {
                            if (event == null) return

                            when (event.sensor.type) {
                                Sensor.TYPE_ACCELEROMETER -> {
                                    System.arraycopy(event.values, 0, accelerometerReading, 0, accelerometerReading.size)
                                }
                                Sensor.TYPE_MAGNETIC_FIELD -> {
                                    System.arraycopy(event.values, 0, magnetometerReading, 0, magnetometerReading.size)
                                }
                            }

                            val rotationMatrix = FloatArray(9)
                            val inclinationMatrix = FloatArray(9)
                            val success = SensorManager.getRotationMatrix(
                                rotationMatrix, inclinationMatrix, accelerometerReading, magnetometerReading
                            )

                            if (success) {
                                val orientation = FloatArray(3)
                                SensorManager.getOrientation(rotationMatrix, orientation)
                                var direction = Math.toDegrees(orientation[0].toDouble())
                                val newAngle = if (direction >= 0) direction else direction + 360

                                var deltaAngle = newAngle - filteredAngle
                                if (deltaAngle > 180) deltaAngle -= 360
                                else if (deltaAngle < -180) deltaAngle += 360

                                filteredAngle += alpha * deltaAngle

                                if (filteredAngle >= 360) filteredAngle -= 360
                                else if (filteredAngle < 0) filteredAngle += 360

                                eventSink?.success(filteredAngle)
                            }
                        }

                        override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {}
                    }

                    sensorManager.registerListener(sensorListener, accelerometer, SensorManager.SENSOR_DELAY_UI)
                    sensorManager.registerListener(sensorListener, magnetometer, SensorManager.SENSOR_DELAY_UI)
                }

                override fun onCancel(arguments: Any?) {
                    sensorListener?.let {
                        sensorManager.unregisterListener(it)
                    }
                    sensorListener = null
                    eventSink = null
                }
            }
        )
    }

    // Facebook Analytics tracking methods
    private fun logFacebookEvent(eventName: String, parameters: Map<String, Any>) {
        try {
            appEventsLogger?.let { logger ->
                val bundle = Bundle()
                for ((key, value) in parameters) {
                    when (value) {
                        is String -> bundle.putString(key, value)
                        is Int -> bundle.putInt(key, value)
                        is Double -> bundle.putDouble(key, value)
                        is Boolean -> bundle.putBoolean(key, value)
                        else -> bundle.putString(key, value.toString())
                    }
                }
                logger.logEvent(eventName, bundle)
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Failed to log Facebook event: $eventName", e)
        }
    }

    private fun logCompassUsage() {
        try {
            appEventsLogger?.let { logger ->
                val parameters = Bundle()
                parameters.putString("feature", "compass")
                parameters.putString("action", "view_compass")
                parameters.putLong("timestamp", System.currentTimeMillis())
                logger.logEvent("compass_usage", parameters)
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Failed to log compass usage", e)
        }
    }

    private fun logScreenView(screenName: String) {
        try {
            appEventsLogger?.let { logger ->
                val parameters = Bundle()
                parameters.putString("screen_name", screenName)
                parameters.putLong("timestamp", System.currentTimeMillis())
                logger.logEvent(AppEventsConstants.EVENT_NAME_VIEWED_CONTENT, parameters)
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Failed to log screen view: $screenName", e)
        }
    }

    override fun onResume() {
        super.onResume()
        try {
            appEventsLogger?.let { logger ->
                // Track app resume
                val parameters = Bundle()
                parameters.putString("action", "app_resume")
                logger.logEvent("app_activity", parameters)
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Failed to track app resume", e)
        }
    }

    override fun onPause() {
        super.onPause()
        try {
            appEventsLogger?.let { logger ->
                // Track app pause
                val parameters = Bundle()
                parameters.putString("action", "app_pause")
                logger.logEvent("app_activity", parameters)
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Failed to track app pause", e)
        }
    }

    // Helper method to detect emulator
    private fun isRunningOnEmulator(): Boolean {
        return (android.os.Build.FINGERPRINT.startsWith("generic")
                || android.os.Build.FINGERPRINT.startsWith("unknown")
                || android.os.Build.MODEL.contains("google_sdk")
                || android.os.Build.MODEL.contains("Emulator")
                || android.os.Build.MODEL.contains("Android SDK built for x86")
                || android.os.Build.MANUFACTURER.contains("Genymotion")
                || (android.os.Build.BRAND.startsWith("generic") && android.os.Build.DEVICE.startsWith("generic"))
                || "google_sdk" == android.os.Build.PRODUCT)
    }

    // Provide fake compass data for emulator testing
    private fun provideFakeCompassData(events: EventChannel.EventSink?) {
        android.util.Log.d("MainActivity", "Starting fake compass data for emulator")

        // Create a timer to send fake compass data
        val handler = android.os.Handler(android.os.Looper.getMainLooper())
        var angle = 0.0

        val runnable = object : Runnable {
            override fun run() {
                // Simulate slowly rotating compass
                angle += 2.0 // Rotate 2 degrees each update
                if (angle >= 360.0) angle = 0.0

                events?.success(angle)
                android.util.Log.d("MainActivity", "Fake compass data: $angle")

                // Schedule next update in 100ms
                handler.postDelayed(this, 100)
            }
        }

        // Start sending fake data
        handler.post(runnable)
    }
}
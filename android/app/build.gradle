plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.hellovietnam.compassapp_vn"
    compileSdkVersion 35

    defaultConfig {
        applicationId = "com.hellovietnam.compassapp_vn"
        minSdkVersion 21
        targetSdkVersion 35
        versionCode 18
        versionName "1.0"
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = "11"
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.debug
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation 'com.facebook.android:facebook-android-sdk:[16.0,17.0)'
}
